import json
import re
import uuid
import logging
import time # Add time import
from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config
from nebula3.gclient.net.Session import Session # Import Session for type hinting in comments
import traceback # Add traceback for detailed error logging
import openai
from openai import OpenAI # Import OpenAI client

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 辅助函数 ---

def parse_schema_from_prompt(prompt_content):
    """
    从提示词内容中解析Schema定义。
    这里简化处理，直接硬编码Schema，因为提示词是固定的。
    在更复杂的场景下，需要通过正则表达式或NLP方法从文本中动态解析。
    """
    entity_types = {
        "PERSON": ["name", "title"],
        "ORGANIZATION": ["name", "founding_date", "stock_code"],
        "PRODUCT": ["name"],
        "LOCATION": ["name"],
        "DATE": ["name"]
    }
    relation_types = {
        "works_for": ("PERSON", "ORGANIZATION"),
        "founded_by": ("ORGANIZATION", "PERSON"),
        "located_in": ("ORGANIZATION", "LOCATION"),
        "product_of": ("PRODUCT", "ORGANIZATION")
    }
    return {"entity_types": entity_types, "relation_types": relation_types}

def escape_string(text):
    """Escapes double quotes in a string for nGQL."""
    if isinstance(text, str):
        return text.replace('"', '\"')
    return str(text)


def connect_to_nebulagraph(config):
    """
    连接到NebulaGraph。
    """
    logging.info(f"尝试连接到NebulaGraph: {config['host']}:{config['port']}, 空间: {config['space_name']}")
    
    config_ng = Config()
    config_ng.max_connection_pool_size = 10 # Example: set max connection pool size
    pool = ConnectionPool()
    addresses = [(config['host'], config['port'])]
    ok = pool.init(addresses, config_ng)
    if not ok:
        logging.error("NebulaGraph 连接池初始化失败。")
        raise Exception("NebulaGraph Connection Pool Init Failed")

    # 获取Session，并选择空间
    session: Session = None
    try:
        session = pool.get_session(config['username'], config['password'])
        if session:
            logging.info(f"成功连接到NebulaGraph: {config['host']}:{config['port']}")
            return session
        else:
            raise Exception("获取NebulaGraph会话失败。")
    except Exception as e:
        log_error(f"NebulaGraph 连接或会话获取失败: {e}", traceback.format_exc())
        if session:
            pool.close()
        raise

def close_nebulagraph_connection(session: Session):
    """
    关闭NebulaGraph连接池中的会话。
    """
    if session:
        logging.info("关闭NebulaGraph会话。")
        session.release()


def create_or_check_nebulagraph_schema(session: Session, schema_def, space_name):
    """
    在NebulaGraph中创建或检查Tag和Edge Type。
    """
    logging.info(f"准备创建或检查NebulaGraph Schema in space: {space_name}")

    # 创建或使用知识空间
    create_space_query = f"CREATE SPACE IF NOT EXISTS `{space_name}`(vid_type=FIXED_STRING(256));"
    logging.info(f"nGQL: {create_space_query}")
    resp = session.execute(create_space_query)
    if not resp.is_succeeded():
        log_error(f"创建空间 '{space_name}' 失败: {resp.error_msg()}", resp.error_msg())
        raise Exception(f"创建空间 '{space_name}' 失败: {resp.error_msg()}")

    logging.info(f"空间 '{space_name}' 创建/检查完成。等待空间同步...")
    time.sleep(10) # 留出时间等待空间同步

    use_space_query = f"USE `{space_name}`;"
    logging.info(f"nGQL: {use_space_query}")
    resp = session.execute(use_space_query)
    if not resp.is_succeeded():
        log_error(f"使用空间 '{space_name}' 失败: {resp.error_msg()}", resp.error_msg())
        raise Exception(f"使用空间 '{space_name}' 失败: {resp.error_msg()}")

    # 创建Tag
    for entity_type, attributes in schema_def["entity_types"].items():
        props = []
        if entity_type == "PERSON":
            props = ["name string", "title string"]
        elif entity_type == "ORGANIZATION":
            props = ["name string", "founding_date string", "stock_code string"]
        elif entity_type == "PRODUCT" or entity_type == "LOCATION" or entity_type == "DATE":
            props = ["name string"]
        
        props_str = ", ".join(props)
        query = f"CREATE TAG IF NOT EXISTS `{entity_type}`({props_str});"
        logging.info(f"nGQL: {query}")
        resp = session.execute(query)
        if not resp.is_succeeded():
            log_error(f"创建Tag '{entity_type}' 失败: {resp.error_msg()}", resp.error_msg())
            raise Exception(f"创建Tag '{entity_type}' 失败: {resp.error_msg()}")

    # 创建Edge Type
    for relation_type in schema_def["relation_types"].keys():
        query = f"CREATE EDGE IF NOT EXISTS `{relation_type}`();"
        logging.info(f"nGQL: {query}")
        resp = session.execute(query)
        if not resp.is_succeeded():
            log_error(f"创建Edge Type '{relation_type}' 失败: {resp.error_msg()}", resp.error_msg())
            raise Exception(f"创建Edge Type '{relation_type}' 失败: {resp.error_msg()}")

    logging.info("NebulaGraph Schema创建/检查完成。等待Schema同步...")
    time.sleep(10) # 留出时间等待Schema同步

def chunk_document(content, max_tokens):
    """
    将长文档内容分割成更小的块，以适应LLM的token限制。
    简单的模拟，实际应根据LLM的token限制和文本结构进行分割
    """
    if len(content) > max_tokens * 4: # 假设平均一个汉字4个token
        logging.warning("文档内容过长，可能超出LLM的token限制。请实现更智能的分块策略。")
    return [content]

def get_prompt_template():
    """
    获取大模型提示词模板。
    """
    return """# 角色
你是一位顶级的知识图谱构建专家，擅长从非结构化文本中一次性提取所有实体、关系和属性。

# 任务
你的任务是分析下面的【源文本】，并严格按照我定义的【Schema】和【输出格式】提取信息。

# Schema 定义
1.  **实体类型 (Entity Types)**:
    - PERSON: 人名
    - ORGANIZATION: 组织机构名
    - PRODUCT: 产品名称
    - LOCATION: 地理位置
    - DATE: 日期

2.  **实体属性 (Attributes per Entity Type)**:
    - PERSON:
        - "title": 职位或头衔
    - ORGANIZATION:
        - "founding_date": 成立日期
        - "stock_code": 股票代码

3.  **关系类型 (Relation Types)**:
    - works_for (任职于): (PERSON, ORGANIZATION)
    - founded_by (创始人是): (ORGANIZATION, PERSON)
    - located_in (位于): (ORGANIZATION, LOCATION)
    - product_of (产品属于): (PRODUCT, ORGANIZATION)

# 思维链指引 (Chain of Thought)
请按照以下步骤思考和执行：
1.  通读【源文本】，识别出所有符合【实体类型】定义的实体。
2.  对于每个识别出的实体，再次阅读文本，找到并提取其对应的【实体属性】。
3.  分析实体之间的联系，识别出符合【关系类型】定义的关系三元组。
4.  最后，将所有提取的信息整合到指定的JSON结构中。

# 输出格式
请将最终结果组织成一个单一的JSON对象，该对象包含两个键："entities" 和 "relations"。
- "entities": 一个JSON数组，每个对象代表一个实体，包含 "id" (唯一标识符，从1开始), "text", "type", 和 "attributes"。
- "relations": 一个JSON数组，每个对象代表一个关系三元组，包含 "head_id", "relation", 和 "tail_id"，id对应实体列表中的id。

# 示例
【源文本】: "雷军在北京创立了小米集团，并发布了小米手机。"
【输出】:
{{
  "entities": [
    {{
      "id": 1,
      "text": "雷军",
      "type": "PERSON",
      "attributes": {{}}
    }},
    {{
      "id": 2,
      "text": "北京",
      "type": "LOCATION",
      "attributes": {{}}
    }},
    {{
      "id": 3,
      "text": "小米集团",
      "type": "ORGANIZATION",
      "attributes": {{}}
    }},
    {{
      "id": 4,
      "text": "小米手机",
      "type": "PRODUCT",
      "attributes": {{}}
    }}
  ],
  "relations": [
    {{
      "head_id": 3,
      "relation": "founded_by",
      "tail_id": 1
    }},
    {{
      "head_id": 3,
      "relation": "located_in",
      "tail_id": 2
    }},
    {{
      "head_id": 4,
      "relation": "product_of",
      "tail_id": 3
    }}
  ]
}}

# 开始处理
【源文本】:
{content}

【输出】:
"""


def call_llm_api(endpoint, api_key, prompt, model_name="Qwen2.5-72B-Instruct-GPTQ-Int4"):
    """
    调用大模型API进行知识抽取。
    """
    logging.info(f"调用LLM API: {endpoint}, 模型: {model_name}")
    logging.debug(f"LLM Prompt:\n{prompt[:200]}...") # 打印部分prompt

    try:
        client = OpenAI(api_key=api_key, base_url=endpoint)

        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": prompt},
            ],
            stream=False,
            temperature=0.0, # 确保输出确定性
        )
        
        llm_output = completion.choices[0].message.content
        logging.debug(f"LLM Raw Output:\n{llm_output[:200]}...")
        
        # 尝试从LLM输出中提取JSON部分
        # 假设JSON总是包含在 ```json ... ``` 块中
        json_match = re.search(r'```json\n(.*?)```', llm_output, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            return json.loads(json_str)
        else:
            # 如果没有找到```json```块，尝试直接解析整个输出为JSON
            logging.warning("LLM响应中未找到JSON代码块，尝试直接解析整个响应。")
            return json.loads(llm_output)

    except json.JSONDecodeError as e:
        log_error(f"LLM响应JSON解析失败: {e}", f"Response: {llm_output}, Traceback: {traceback.format_exc()}")
        return {"entities": [], "relations": []}
    except Exception as e:
        log_error(f"调用LLM API失败: {e}", traceback.format_exc())
        return {"entities": [], "relations": []}

def parse_llm_output_json(json_response):
    """
    解析大模型返回的JSON，提取entities和relations。
    """
    try:
        return json_response
    except json.JSONDecodeError as e:
        logging.error(f"LLM响应JSON解析失败: {e}, 响应内容: {json_response}")
        return {"entities": [], "relations": []}

def normalize_extracted_data(parsed_data, global_llm_id_to_vid_map, entity_name_to_vid_map):
    """
    规范化实体和关系，生成或查找VID，并更新全局映射。
    global_llm_id_to_vid_map: 存储 {chunk_id_llm_id: vid}，用于关系查找
    entity_name_to_vid_map: 存储 {entity_type_entity_name: vid}，用于实体去重和跨块ID统一
    """
    normalized_entities = []
    normalized_relations = []
    
    entities = parsed_data.get("entities", [])
    relations = parsed_data.get("relations", [])

    current_chunk_id_map = {} # 存储当前块LLM id到VID的映射

    for entity in entities:
        llm_id = entity["id"]
        text = entity["text"]
        entity_type = entity["type"]
        attributes = entity.get("attributes", {})

        # 构建一个基于类型和文本的唯一键，用于实体去重
        unique_entity_key = f"{entity_type}:{text}"
        
        vid = None
        if unique_entity_key in entity_name_to_vid_map:
            # 实体已存在，使用已有的VID
            vid = entity_name_to_vid_map[unique_entity_key]
            logging.debug(f"实体 '{text}' ({entity_type}) 已存在，使用 VID: {vid}")
        else:
            # 新实体，生成新的VID
            # 推荐使用 entity_type:entity_text 作为 VID，确保唯一性和可读性
            # 或者使用 UUID.uuid4().hex
            vid = f"{entity_type}:{text}" if len(f"{entity_type}:{text}") <= 256 else uuid.uuid4().hex
            entity_name_to_vid_map[unique_entity_key] = vid
            logging.debug(f"生成新实体 VID: {vid} for '{text}' ({entity_type})")

            normalized_entities.append({
                "vid": vid,
                "text": text,
                "type": entity_type,
                "attributes": attributes
            })
        
        # 更新当前块的LLM ID到实际VID的映射
        current_chunk_id_map[llm_id] = vid
        # 同时更新全局映射，以供关系查找使用（如果存在跨块关系）
        # 这里假设LLM的ID在每个chunk内是独立的，所以需要结合chunk_id（如果chunking），
        # 但我们这里不分chunk，所以直接用LLM ID作为键
        # 考虑到可能存在LLM的ID在不同chunk中重复，这里直接用current_chunk_id_map即可
        # 实际场景中，如果分块且LLM ID可能重复，需要更复杂的全局ID映射策略

    for relation in relations:
        head_llm_id = relation["head_id"]
        tail_llm_id = relation["tail_id"]
        relation_type = relation["relation"]

        head_vid = current_chunk_id_map.get(head_llm_id)
        tail_vid = current_chunk_id_map.get(tail_llm_id)

        if head_vid and tail_vid:
            normalized_relations.append({
                "head_vid": head_vid,
                "relation_type": relation_type,
                "tail_vid": tail_vid
            })
        else:
            logging.warning(f"无法为关系 ({head_llm_id})--[{relation_type}]-->({tail_llm_id}) 找到对应的VID。跳过此关系。")
            
    return normalized_entities, normalized_relations

def remove_duplicate_entities(entities_list):
    """
    根据VID去重实体。
    """
    unique_entities = {}
    for entity in entities_list:
        unique_entities[entity["vid"]] = entity
    return list(unique_entities.values())

def remove_duplicate_relations(relations_list):
    """
    根据头尾VID和关系类型去重关系。
    """
    unique_relations = set()
    result_relations = []
    for rel in relations_list:
        # 创建一个可哈希的元组作为唯一标识
        rel_key = (rel["head_vid"], rel["relation_type"], rel["tail_vid"])
        if rel_key not in unique_relations:
            unique_relations.add(rel_key)
            result_relations.append(rel)
    return result_relations

def batch_insert_vertices(session: Session, entities):
    """
    批量插入实体（节点）的nGQL语句。
    """
    if not entities:
        return

    # 按实体类型分组，便于构建批量插入语句
    grouped_entities = {}
    for entity in entities:
        entity_type = entity["type"]
        if entity_type not in grouped_entities:
            grouped_entities[entity_type] = []
        grouped_entities[entity_type].append(entity)

    for entity_type, entity_list in grouped_entities.items():
        # 获取该实体类型的所有属性名 (这里需要根据 Schema 定义来获取，简化为固定属性)
        props = []
        if entity_type == "PERSON":
            props = ["name", "title"]
        elif entity_type == "ORGANIZATION":
            props = ["name", "founding_date", "stock_code"]
        elif entity_type == "PRODUCT" or entity_type == "LOCATION" or entity_type == "DATE":
            props = ["name"]
        else:
            logging.warning(f"未知实体类型: {entity_type}, 无法构建属性列表。")
            continue
        
        prop_keys_str = ", ".join([f"`{p}`" for p in props])
        values_str_parts = []
        for entity in entity_list:
            vid = escape_string(entity["vid"])
            text = escape_string(entity["text"])
            attrs = entity["attributes"]

            # 构建属性值字符串
            attr_values = []
            if entity_type == "PERSON":
                attr_values.append(f'"{text}"')
                attr_values.append(f'"{escape_string(attrs.get("title", ""))}"')
            elif entity_type == "ORGANIZATION":
                attr_values.append(f'"{text}"')
                attr_values.append(f'"{escape_string(attrs.get("founding_date", ""))}"')
                attr_values.append(f'"{escape_string(attrs.get("stock_code", ""))}"')
            elif entity_type == "PRODUCT" or entity_type == "LOCATION" or entity_type == "DATE":
                attr_values.append(f'"{text}"')
            
            values_str_parts.append(f'"{vid}":({", ".join(attr_values)})')
        
        if values_str_parts:
            values_clause = ", ".join(values_str_parts)
            query = f"INSERT VERTEX `{entity_type}`({prop_keys_str}) VALUES {values_clause};"
            logging.info(f"nGQL: {query[:200]}...") # Log truncated query
            resp = session.execute(query)
            if not resp.is_succeeded():
                log_error(f"批量插入Tag '{entity_type}' 失败: {resp.error_msg()}", resp.error_msg())
                raise Exception(f"批量插入Tag '{entity_type}' 失败: {resp.error_msg()}")

    logging.info("批量实体插入nGQL完成。")

def batch_insert_edges(session: Session, relations):
    """
    批量插入关系（边）的nGQL语句。
    """
    if not relations:
        return
    
    # 按关系类型分组
    grouped_relations = {}
    for rel in relations:
        rel_type = rel["relation_type"]
        if rel_type not in grouped_relations:
            grouped_relations[rel_type] = []
        grouped_relations[rel_type].append(rel)

    for rel_type, rel_list in grouped_relations.items():
        values_str_parts = []
        for rel in rel_list:
            head_vid = escape_string(rel["head_vid"])
            tail_vid = escape_string(rel["tail_vid"])
            values_str_parts.append(f'"{head_vid}"->"{tail_vid}":()')
        
        if values_str_parts:
            values_clause = ", ".join(values_str_parts)
            query = f"INSERT EDGE `{rel_type}`() VALUES {values_clause};"
            logging.info(f"nGQL: {query[:200]}...") # Log truncated query
            resp = session.execute(query)
            if not resp.is_succeeded():
                log_error(f"批量插入Edge '{rel_type}' 失败: {resp.error_msg()}", resp.error_msg())
                raise Exception(f"批量插入Edge '{rel_type}' 失败: {resp.error_msg()}")
            
    logging.info("批量关系插入nGQL完成。")

def log_success(message):
    logging.info(message)

def log_error(message, details):
    logging.error(f"{message}\nDetails:\n{details}")

# --- 主算法实现 ---

def intelligent_kg_extraction_and_storage(document_content, llm_api_endpoint, llm_api_key, nebulagraph_config):
    """
    智能知识图谱抽取与存储算法。
    """
    nebulagraph_session = None
    try:
        logging.info("---------- 算法开始 ----------")
        prompt_template_content = get_prompt_template()

        # 步骤 1: 环境与Schema准备
        schema_def = parse_schema_from_prompt(prompt_template_content)
        nebulagraph_session = connect_to_nebulagraph(nebulagraph_config)
        create_or_check_nebulagraph_schema(nebulagraph_session, schema_def, nebulagraph_config['space_name'])

        # 存储所有抽取到的实体和关系，用于批量插入
        all_extracted_entities = []
        all_extracted_relations = []
        # 用于实体去重和跨块ID统一的映射
        entity_name_to_vid_map = {} 

        # 步骤 2: 文档分块与提示词构建
        # 简化处理，直接将整个文档作为一个chunk
        text_chunks = chunk_document(document_content, max_tokens=2000) # 假设LLM最大处理2000 token

        for chunk_id, chunk in enumerate(text_chunks):
            logging.info(f"处理文档块 {chunk_id + 1}/{len(text_chunks)}...")
            current_prompt = prompt_template_content.format(content=chunk)

            # 步骤 3: 大模型知识抽取
            llm_response_json = call_llm_api(llm_api_endpoint, llm_api_key, current_prompt)

            # 步骤 4: 知识解析与规范化
            parsed_data = parse_llm_output_json(llm_response_json)
            
            # 由于我们在这里没有真正的跨chunk ID管理，global_llm_id_to_vid_map 保持为空
            # 实体去重在最后统一处理
            normalized_entities, normalized_relations = normalize_extracted_data(
                parsed_data, {}, entity_name_to_vid_map
            )

            all_extracted_entities.extend(normalized_entities)
            all_extracted_relations.extend(normalized_relations)

        # 步骤 5: NebulaGraph数据存储
        # 进行最终的实体和关系去重
        unique_entities = remove_duplicate_entities(all_extracted_entities)
        unique_relations = remove_duplicate_relations(all_extracted_relations)

        # 批量插入顶点（实体）
        batch_insert_vertices(nebulagraph_session, unique_entities)
        
        # 批量插入边（关系），此时 global_llm_id_to_vid_map 已被 entity_name_to_vid_map 替代
        # 注意：这里需要确保关系中的head_vid和tail_vid已经在entity_name_to_vid_map中被正确映射
        batch_insert_edges(nebulagraph_session, unique_relations) # Removed global_llm_id_to_vid_map as it's not used here

        log_success("知识图谱抽取与存储成功完成。")
        logging.info("---------- 算法结束 ----------")
        return True

    except Exception as e:
        # 步骤 6: 错误处理与日志
        log_error(f"知识图谱抽取与存储失败: {e}", traceback.format_exc())
        logging.info("---------- 算法异常终止 ----------")
        return False
    finally:
        if nebulagraph_session:
            close_nebulagraph_connection(nebulagraph_session)


# --- 示例用法 ---
if __name__ == "__main__":
    # 示例文档内容1
    document_content_1 = "雷军在北京创立了小米集团，并发布了小米手机。"

    # 示例文档内容2 (将触发模拟LLM的另一个分支)
    document_content_2 = "李明在腾讯工作，职位是高级工程师。腾讯成立于1998年11月11日，股票代码是0700.HK。此外，王芳也是腾讯的一名员工。"
    
    # 示例文档内容3 (结合多个模拟LLM分支)
    document_content_3 = """
    雷军在北京创立了小米集团，并发布了小米手机。
    李明在腾讯工作，职位是高级工程师。腾讯成立于1998年11月11日，股票代码是0700.HK。
    王芳也在腾讯工作。
    """

    llm_endpoint = "http://**************:8000/v1"
    llm_key = "123456"

    nebulagraph_config = {
        "host": "*************",
        "port": 9669,
        "username": "root",
        "password": "123456",
        "space_name": "kg_test"
    }

    print("\n--- 运行示例 3 ---")
    intelligent_kg_extraction_and_storage(
        document_content_3,
        llm_endpoint,
        llm_key,
        nebulagraph_config
    )

    # print("\n--- 运行示例 2 ---")
    # intelligent_kg_extraction_and_storage(
    #     document_content_2,
    #     llm_endpoint,
    #     llm_key,
    #     nebulagraph_config,
    #     prompt_template_content
    # )

    # print("\n--- 运行示例 3 (包含重复实体和关系) ---")
    # intelligent_kg_extraction_and_storage(
    #     document_content_3,
    #     llm_endpoint,
    #     llm_key,
    #     nebulagraph_config,
    #     prompt_template_content
    # )
